#include "pch.h"
#include "aimbot.hpp"
#include "../../entity.hpp"
#include "../../globals.hpp"
#include "../../gamedata.hpp"
#include <thread>
#include <chrono>
#include <cmath>

// ===========================
// AIMBOT IMPLEMENTATION
// ===========================

void Aimbot::doAimbot(const Reader& reader)
{
	// sleep for 1ms to save cpu %
	std::this_thread::sleep_for(std::chrono::milliseconds(1));

	// get our view_matrix
	view_matrix_t viewMatrix = GameData::getViewMatrix();
	auto playerList = reader.getPlayerListCopy();

	// create our playerPositions vector, although i would recommend moving this out of the loop.
	std::vector<Vector> playerPositions;

	// clear our playerPositions vector to remove old players
	playerPositions.clear();

	for (const auto& player : playerList)
	{
		int localTeam = GameData::getLocalTeam();
		if (player.team == localTeam && globals::Legitbot::teamcheck)
			continue;

		// get the 3D head position of the player we're CURRENTLY looping through.
		Vector playerPosition = driver::read_memory<Vector>(GameVars::getInstance()->getDriver(), player.BoneArray + bones::head * 32);

		// convert head position to screen coordinates
		Vector screenPos;
		if (Vector::world_to_screen(viewMatrix, playerPosition, screenPos))
		{
			// add the head screen position to our vector
			playerPositions.push_back(screenPos);
		}
	}

	// check if the user is holding the X key.
	if (GetAsyncKeyState(0x58)) // X on keyboard
	{
		// find the closest player and store it in a variable
		auto closest_player = findClosest(playerPositions);

		// move the mouse to the player
		MoveMouseToPlayer(closest_player);
	}
}

Vector Aimbot::findClosest(const std::vector<Vector> playerPositions)
{
	// check if the player positions vector is empty, if it is then just break out of the function.
	if (playerPositions.empty())
	{
		return { 0.0f, 0.0f, 0.0f };
	}

	// get the center of the screen to be able to subtract the playerPosition by the center of the screen so we know where they are on the screen.
	Vector center_of_screen{ (float)GetSystemMetrics(0) / 2, (float)GetSystemMetrics(1) / 2, 0.0f };

	// keep track of the lowest distance found
	float lowestDistance = 10000.0f;
	Vector closestPlayer = { 0.0f, 0.0f, 0.0f };

	// loop through every single vector using range-based for loop
	for (const auto& pos : playerPositions)
	{
		// calculate distance from center of screen
		float dx = pos.x - center_of_screen.x;
		float dy = pos.y - center_of_screen.y;
		float distance = dx * dx + dy * dy; // squared distance is fine for comparison

		// if the distance is lower than the last vector we checked, then save it
		if (distance < lowestDistance) {
			lowestDistance = distance;
			closestPlayer = pos;
		}
	}

	// return the closest player position
	return closestPlayer;
}

void Aimbot::MoveMouseToPlayer(Vector position)
{
	// check if the position is valid, make a function for this for better practice. this is also just ugly.
	if (position.IsZero())
		return;

	// get the center of our screen.
	Vector center_of_screen{ (float)GetSystemMetrics(0) / 2, (float)GetSystemMetrics(1) / 2, 0.0f };

	// get our new x and y, by subtracting the position by the center of the screen, giving us a position to move the mouse to.
	auto new_x = position.x - center_of_screen.x;
	auto new_y = position.y - center_of_screen.y;

	// move the mouse to said position.
	mouse_event(MOUSEEVENTF_MOVE, new_x, new_y, 0, 0);
}